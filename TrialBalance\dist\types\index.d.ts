export interface XeroRequestData {
    companyId: string;
    tenantId?: string;
    startDate?: string;
    endDate?: string;
}
export interface ProcessedTrialBalanceData {
    Year: number;
    Month: number;
    AccountId: string;
    AccountName: string;
    Amount: number;
    CompanyId: string;
    monthEndDebitAmount: number;
    monthEndCreditAmount: number;
    netChangeAmount: number;
}
export interface XeroError {
    Type: string;
    Title: string;
    Detail: string;
    ValidationErrors?: Array<{
        Message: string;
        Source: string;
    }>;
}
export declare class ValidationError extends Error {
    constructor(message: string);
}
export interface XeroTrialBalanceReport {
    ReportID: string;
    ReportName: string;
    ReportType: string;
    ReportTitles: string[];
    ReportDate: string;
    UpdatedDateUTC: string;
    Fields: any[];
    Rows: XeroTrialBalanceRow[];
}
export interface XeroTrialBalanceRow {
    RowType: string;
    Cells: XeroTrialBalanceCell[];
    Title?: string;
    Rows?: XeroTrialBalanceRow[];
}
export interface XeroTrialBalanceCell {
    Value: string;
    Attributes?: Array<{
        Value: string;
        Id: string;
    }>;
}
export interface MonthInfo {
    year: number;
    month: number;
    startDate: string;
    endDate: string;
}
export interface ProductionConfig {
    API_TIMEOUT_MS: number;
    MAX_CONCURRENT_REQUESTS: number;
    RETRY_ATTEMPTS: number;
    RETRY_DELAY_MS: number;
}
export interface CompanyIntegration {
    Id: string;
    XeroAccessToken: string | null;
    XeroTenantId: string | null;
    XeroTokenExpiry: Date | null;
    XeroRefreshToken: string | null;
    XeroRefreshTokenExpiry: Date | null;
}
export interface SyncPeriod {
    startDate: Date;
    endDate: Date;
    monthsToSync: number;
    isInitialSync: boolean;
}
export interface ApiLogEntry {
    id: string;
    userId?: string;
    companyId: string;
    method: string;
    apiUrl: string;
    status: string;
    integrationName: string;
    duration: string;
    apiName: string;
    apiRequest?: any;
    apiResponse?: any;
    isActive: boolean;
    createdAt: Date;
}
export interface SyncLogEntry {
    id: string;
    requestId?: string;
    entity: string;
    integration: string;
    apiEndpoint?: string;
    method?: string;
    status: 'PENDING' | 'IN_PROGRESS' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'RETRYING' | 'CANCELLED';
    message?: string;
    duration?: string;
    retryCount: number;
    maxRetries: number;
    lastRetryAt?: Date;
    nextRetryAt?: Date;
    startedAt: Date;
    completedAt?: Date;
    companyId: string;
    userId?: string;
    requestPayload?: any;
    responsePayload?: any;
    errorDetails?: any;
    createdAt: Date;
    updatedAt: Date;
}
//# sourceMappingURL=index.d.ts.map
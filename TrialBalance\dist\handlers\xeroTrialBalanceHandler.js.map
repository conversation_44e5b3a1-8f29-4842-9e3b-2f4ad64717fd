{"version": 3, "file": "xeroTrialBalanceHandler.js", "sourceRoot": "", "sources": ["../../src/handlers/xeroTrialBalanceHandler.ts"], "names": [], "mappings": ";;;;;;AAoCA,oCAA4D;AAC5D,yEAA6E;AAC7E,0DAA0D;AAE1D,oDAA4B;AAC5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AAgBT,MAAM,OAAO,GAAG,KAAK,EACxB,KAAsC,EACtC,OAAgB,EACqB,EAAE;IACvC,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IACvD,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;IAE1E,IAAI,CAAC;QACD,IAAI,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;YAE9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,CAAC,OAAO,CAAC,MAAM,eAAe,CAAC,CAAC;YAElE,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAoB,CAAC;oBAC/D,OAAO,CAAC,GAAG,CAAC,iDAAiD,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC;oBAEtF,MAAM,IAAA,gDAA0B,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC;oBAEvD,OAAO,CAAC,GAAG,CAAC,4DAA4D,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC;gBACrG,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBAClB,OAAO,CAAC,KAAK,CAAC,gDAAgD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;wBAC/F,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;wBAClB,SAAS,EAAE,MAAM,CAAC,SAAS;qBAC9B,CAAC,CAAC;oBACH,MAAM,KAAK,CAAC;gBAChB,CAAC;YACL,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YACzD,OAAO;QAEX,CAAC;aAAM,CAAC;YAEJ,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;YAEjD,IAAI,CAAC;gBACD,MAAM,WAAW,GAAG,IAAA,gCAAgB,EAAC,KAA6B,CAAC,CAAC;gBACpE,OAAO,CAAC,GAAG,CAAC,+CAA+C,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC;gBAEpF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,IAAA,gDAA0B,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAExC,OAAO,CAAC,GAAG,CAAC,qCAAqC,QAAQ,mBAAmB,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC;gBAErG,OAAO;oBACH,UAAU,EAAE,GAAG;oBACf,OAAO,EAAE;wBACL,cAAc,EAAE,kBAAkB;wBAClC,mBAAmB,EAAE,QAAQ,CAAC,QAAQ,EAAE;qBAC3C;oBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;wBACjB,OAAO,EAAE,IAAI;wBACb,OAAO,EAAE,gDAAgD;wBACzD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,gBAAgB,EAAE,QAAQ;wBAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;qBACnC,CAAC;iBACL,CAAC;YAEN,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBAClB,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE;oBAC3C,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,SAAS,EAAE,OAAO,CAAC,YAAY;iBAClC,CAAC,CAAC;gBAGH,IAAI,UAAU,GAAG,GAAG,CAAC;gBACrB,IAAI,KAAK,YAAY,uBAAe,EAAE,CAAC;oBACnC,UAAU,GAAG,GAAG,CAAC;gBACrB,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBAC7C,UAAU,GAAG,GAAG,CAAC;gBACrB,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrF,UAAU,GAAG,GAAG,CAAC;gBACrB,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC9C,UAAU,GAAG,GAAG,CAAC;gBACrB,CAAC;gBAED,OAAO;oBACH,UAAU;oBACV,OAAO,EAAE;wBACL,cAAc,EAAE,kBAAkB;qBACrC;oBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;wBACjB,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACnC,SAAS,EAAE,OAAO,CAAC,YAAY;qBAClC,CAAC;iBACL,CAAC;YACN,CAAC;QACL,CAAC;IAEL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QAClB,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE;YACzC,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,OAAO,CAAC,YAAY;SAClC,CAAC,CAAC;QAGH,IAAI,SAAS,IAAI,KAAK,EAAE,CAAC;YACrB,MAAM,KAAK,CAAC;QAChB,CAAC;QAGD,OAAO;YACH,UAAU,EAAE,GAAG;YACf,OAAO,EAAE;gBACL,cAAc,EAAE,kBAAkB;aACrC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACjB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,qEAAqE;gBAC5E,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,SAAS,EAAE,OAAO,CAAC,YAAY;aAClC,CAAC;SACL,CAAC;IACN,CAAC;AACL,CAAC,CAAC;AA1HW,QAAA,OAAO,WA0HlB"}
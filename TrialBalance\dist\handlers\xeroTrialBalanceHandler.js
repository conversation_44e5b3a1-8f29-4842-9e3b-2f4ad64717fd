"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const types_1 = require("../types");
const trialBalanceService_1 = require("../services/trialBalanceService");
const requestParser_1 = require("../utils/requestParser");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const handler = async (event, context) => {
    console.log('🚀 Trial Balance Lambda handler started');
    console.log('📋 Event type:', 'Records' in event ? 'SQS' : 'API Gateway');
    try {
        if ('Records' in event && event.Records?.length) {
            console.log(`📥 Processing ${event.Records.length} SQS messages`);
            for (const record of event.Records) {
                try {
                    const requestData = JSON.parse(record.body);
                    console.log(`🔄 Processing Trial Balance sync for company: ${requestData.companyId}`);
                    await (0, trialBalanceService_1.processTrialBalanceRequest)(requestData, context);
                    console.log(`✅ Successfully processed Trial Balance sync for company: ${requestData.companyId}`);
                }
                catch (error) {
                    console.error(`❌ Failed to process SQS message for company: ${JSON.parse(record.body).companyId}`, {
                        error: error.message,
                        stack: error.stack,
                        messageId: record.messageId
                    });
                    throw error;
                }
            }
            console.log('✅ All SQS messages processed successfully');
            return;
        }
        else {
            console.log('🌐 Processing API Gateway request');
            try {
                const requestData = (0, requestParser_1.parseRequestData)(event);
                console.log(`🔄 Starting Trial Balance sync for company: ${requestData.companyId}`);
                const startTime = Date.now();
                await (0, trialBalanceService_1.processTrialBalanceRequest)(requestData, context);
                const duration = Date.now() - startTime;
                console.log(`✅ Trial Balance sync completed in ${duration}ms for company: ${requestData.companyId}`);
                return {
                    statusCode: 200,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Processing-Time': duration.toString()
                    },
                    body: JSON.stringify({
                        success: true,
                        message: '✅ Trial Balance data synchronized successfully',
                        timestamp: new Date().toISOString(),
                        processingTimeMs: duration,
                        companyId: requestData.companyId
                    }),
                };
            }
            catch (error) {
                console.error('❌ API Gateway request failed:', {
                    error: error.message,
                    stack: error.stack,
                    requestId: context.awsRequestId
                });
                let statusCode = 500;
                if (error instanceof types_1.ValidationError) {
                    statusCode = 400;
                }
                else if (error.message.includes('not found')) {
                    statusCode = 404;
                }
                else if (error.message.includes('authentication') || error.message.includes('token')) {
                    statusCode = 401;
                }
                else if (error.message.includes('rate limit')) {
                    statusCode = 429;
                }
                return {
                    statusCode,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        success: false,
                        error: error.message,
                        timestamp: new Date().toISOString(),
                        requestId: context.awsRequestId
                    }),
                };
            }
        }
    }
    catch (error) {
        console.error('❌ Unexpected handler error:', {
            error: error.message,
            stack: error.stack,
            requestId: context.awsRequestId
        });
        if ('Records' in event) {
            throw error;
        }
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                success: false,
                error: 'Internal server error occurred during Trial Balance synchronization',
                timestamp: new Date().toISOString(),
                requestId: context.awsRequestId
            }),
        };
    }
};
exports.handler = handler;
//# sourceMappingURL=xeroTrialBalanceHandler.js.map
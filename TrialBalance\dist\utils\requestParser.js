"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseRequestData = parseRequestData;
exports.extractRelevantHeaders = extractRelevantHeaders;
const types_1 = require("../types");
function parseRequestData(event) {
    console.log('📝 Parsing request data from API Gateway event');
    let requestBody;
    try {
        requestBody = event.body ? JSON.parse(event.body) : {};
    }
    catch (error) {
        console.error('❌ Failed to parse request body:', error);
        throw new types_1.ValidationError('Invalid JSON in request body');
    }
    const companyId = requestBody.companyId ||
        event.pathParameters?.['companyId'] ||
        event.queryStringParameters?.['companyId'];
    if (!companyId) {
        throw new types_1.ValidationError('companyId is required in request body, path parameters, or query parameters');
    }
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(companyId)) {
        throw new types_1.ValidationError('companyId must be a valid UUID');
    }
    const tenantId = requestBody.tenantId || event.queryStringParameters?.['tenantId'];
    const startDate = requestBody.startDate || event.queryStringParameters?.['startDate'];
    const endDate = requestBody.endDate || event.queryStringParameters?.['endDate'];
    if (startDate && !isValidDateFormat(startDate)) {
        throw new types_1.ValidationError('startDate must be in YYYY-MM-DD format');
    }
    if (endDate && !isValidDateFormat(endDate)) {
        throw new types_1.ValidationError('endDate must be in YYYY-MM-DD format');
    }
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
        throw new types_1.ValidationError('startDate must be before or equal to endDate');
    }
    const requestData = {
        companyId,
        ...(tenantId && { tenantId }),
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
    };
    console.log('✅ Successfully parsed request data:', {
        companyId: requestData.companyId,
        tenantId: requestData.tenantId ? requestData.tenantId.substring(0, 8) + '...' : undefined,
        startDate: requestData.startDate,
        endDate: requestData.endDate,
    });
    return requestData;
}
function isValidDateFormat(dateString) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateString)) {
        return false;
    }
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime()) &&
        date.toISOString().substring(0, 10) === dateString;
}
function extractRelevantHeaders(event) {
    const relevantHeaders = {};
    if (event.headers) {
        const headersToExtract = [
            'user-agent',
            'x-forwarded-for',
            'x-amzn-requestid',
            'content-type',
            'authorization'
        ];
        headersToExtract.forEach(header => {
            const value = event.headers[header] || event.headers[header.toLowerCase()];
            if (value) {
                relevantHeaders[header] = header === 'authorization' ?
                    value.substring(0, 10) + '...' : value;
            }
        });
    }
    return relevantHeaders;
}
//# sourceMappingURL=requestParser.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PRODUCTION_CONFIG = void 0;
exports.getXeroConfig = getXeroConfig;
exports.getDatabaseConfig = getDatabaseConfig;
exports.getStage = getStage;
exports.isProduction = isProduction;
exports.getAwsRegion = getAwsRegion;
function getXeroConfig() {
    const baseUrl = process.env['XERO_BASE_URL'] || 'https://api.xero.com/api.xro/2.0/';
    const clientId = process.env['XERO_CLIENT_ID'];
    const clientSecret = process.env['XERO_CLIENT_SECRET'];
    const redirectUri = process.env['XERO_REDIRECT_URI'];
    if (!clientId || !clientSecret || !redirectUri) {
        throw new Error('Missing required Xero configuration environment variables');
    }
    return {
        baseUrl,
        clientId,
        clientSecret,
        redirectUri,
    };
}
function getDatabaseConfig() {
    const databaseUrl = process.env['DATABASE_URL'];
    if (!databaseUrl) {
        throw new Error('DATABASE_URL environment variable is required');
    }
    return {
        databaseUrl,
    };
}
exports.PRODUCTION_CONFIG = {
    API_TIMEOUT_MS: 30000,
    MAX_CONCURRENT_REQUESTS: 2,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY_MS: 1000,
};
function getStage() {
    return process.env['STAGE'] || 'dev';
}
function isProduction() {
    return getStage() === 'prod';
}
function getAwsRegion() {
    return process.env['AWS_REGION'] || 'us-east-1';
}
//# sourceMappingURL=environment.js.map
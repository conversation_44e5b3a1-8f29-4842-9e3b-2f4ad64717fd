"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshXeroToken = refreshXeroToken;
exports.needsTokenRefresh = needsTokenRefresh;
exports.isRefreshTokenExpired = isRefreshTokenExpired;
const client_1 = require("@prisma/client");
const axios_1 = __importDefault(require("axios"));
const environment_1 = require("../config/environment");
const apiLogService_1 = require("./apiLogService");
const prisma = new client_1.PrismaClient();
async function refreshXeroToken(companyId) {
    console.log(`🔄 Checking Xero token for company: ${companyId}`);
    let refreshStartTime = Date.now();
    try {
        const company = await prisma.company.findUnique({
            where: { Id: companyId },
            select: {
                Id: true,
                XeroAccessToken: true,
                XeroRefreshToken: true,
                XeroTokenExpiry: true,
                XeroRefreshTokenExpiry: true,
            },
        });
        if (!company) {
            throw new Error(`Company not found: ${companyId}`);
        }
        if (!company.XeroAccessToken || !company.XeroRefreshToken) {
            throw new Error(`Missing Xero tokens for company: ${companyId}`);
        }
        const now = new Date();
        const tokenExpiry = company.XeroTokenExpiry;
        const bufferTime = 5 * 60 * 1000;
        if (tokenExpiry && tokenExpiry.getTime() > now.getTime() + bufferTime) {
            console.log('✅ Access token is still valid');
            return company.XeroAccessToken;
        }
        const refreshTokenExpiry = company.XeroRefreshTokenExpiry;
        if (refreshTokenExpiry && refreshTokenExpiry.getTime() <= now.getTime()) {
            throw new Error(`Refresh token expired for company: ${companyId}. Re-authentication required.`);
        }
        console.log('🔄 Access token expired, refreshing...');
        const { clientId, clientSecret } = (0, environment_1.getXeroConfig)();
        const tokenUrl = 'https://identity.xero.com/connect/token';
        refreshStartTime = Date.now();
        const response = await axios_1.default.post(tokenUrl, new URLSearchParams({
            grant_type: 'refresh_token',
            refresh_token: company.XeroRefreshToken,
        }).toString(), {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`,
            },
            timeout: 30000,
        });
        const refreshDuration = Date.now() - refreshStartTime;
        const tokenData = response.data;
        await (0, apiLogService_1.logSuccessfulApiCall)(companyId, 'POST', tokenUrl, refreshDuration, 'TokenRefresh', { grant_type: 'refresh_token' }, { expires_in: tokenData.expires_in });
        if (!tokenData.access_token || !tokenData.refresh_token) {
            throw new Error('Invalid token response from Xero');
        }
        const newTokenExpiry = new Date(now.getTime() + (tokenData.expires_in * 1000));
        const newRefreshTokenExpiry = new Date(now.getTime() + (60 * 24 * 60 * 60 * 1000));
        await prisma.company.update({
            where: { Id: companyId },
            data: {
                XeroAccessToken: tokenData.access_token,
                XeroRefreshToken: tokenData.refresh_token,
                XeroTokenExpiry: newTokenExpiry,
                XeroRefreshTokenExpiry: newRefreshTokenExpiry,
                UpdatedAt: now,
            },
        });
        console.log('✅ Successfully refreshed Xero tokens');
        return tokenData.access_token;
    }
    catch (error) {
        const refreshDuration = Date.now() - refreshStartTime;
        console.error('❌ Failed to refresh Xero token:', {
            companyId,
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
        });
        await (0, apiLogService_1.logFailedApiCall)(companyId, 'POST', 'https://identity.xero.com/connect/token', refreshDuration, 'TokenRefresh', error, { grant_type: 'refresh_token' });
        if (error.response?.status === 400) {
            const errorData = error.response.data;
            if (errorData.error === 'invalid_grant') {
                throw new Error(`Refresh token is invalid or expired for company: ${companyId}. Re-authentication required.`);
            }
        }
        throw new Error(`Token refresh failed for company: ${companyId}. ${error.message}`);
    }
    finally {
        await prisma.$disconnect();
    }
}
function needsTokenRefresh(tokenExpiry, bufferMinutes = 5) {
    if (!tokenExpiry) {
        return true;
    }
    const now = new Date();
    const bufferTime = bufferMinutes * 60 * 1000;
    return tokenExpiry.getTime() <= now.getTime() + bufferTime;
}
function isRefreshTokenExpired(refreshTokenExpiry) {
    if (!refreshTokenExpiry) {
        return true;
    }
    const now = new Date();
    return refreshTokenExpiry.getTime() <= now.getTime();
}
//# sourceMappingURL=refreshTokenService.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractTrialBalanceData = extractTrialBalanceData;
exports.validateTrialBalanceReport = validateTrialBalanceReport;
function extractTrialBalanceData(report, requestData, month) {
    console.log(`🔄 Extracting Trial Balance data for ${month.year}-${month.month}`);
    if (!report.Rows || !Array.isArray(report.Rows)) {
        console.warn('⚠️ No rows found in Trial Balance report');
        return [];
    }
    const results = [];
    for (const row of report.Rows) {
        if (row.RowType === 'Section' && row.Rows) {
            for (const innerRow of row.Rows) {
                const processedData = processTrialBalanceRow(innerRow, requestData, month);
                if (processedData) {
                    results.push(processedData);
                }
            }
        }
        else if (row.RowType === 'Row') {
            const processedData = processTrialBalanceRow(row, requestData, month);
            if (processedData) {
                results.push(processedData);
            }
        }
    }
    console.log(`✅ Extracted ${results.length} Trial Balance records for ${month.year}-${month.month}`);
    return results;
}
function processTrialBalanceRow(row, requestData, month) {
    if (row.RowType !== 'Row' || !row.Cells || row.Cells.length < 5) {
        return null;
    }
    try {
        const accountCell = row.Cells[0];
        const accountName = accountCell?.Value || null;
        if (!accountName || accountName.trim() === '') {
            return null;
        }
        let accountId = null;
        if (accountCell?.Attributes) {
            const accountAttr = accountCell.Attributes.find(attr => attr.Id === 'account');
            accountId = accountAttr?.Value || null;
        }
        if (!accountId) {
            console.log(`⚠️ Skipping row without account ID: ${accountName}`);
            return null;
        }
        const netChangeAmount = parseAmount(row.Cells[3]?.Value) || 0;
        const monthEndBalance = parseAmount(row.Cells[4]?.Value) || 0;
        const monthEndDebitAmount = monthEndBalance >= 0 ? Math.abs(monthEndBalance) : 0;
        const monthEndCreditAmount = monthEndBalance < 0 ? Math.abs(monthEndBalance) : 0;
        const processedData = {
            Year: month.year,
            Month: month.month,
            AccountId: accountId,
            AccountName: accountName.trim(),
            Amount: monthEndBalance,
            CompanyId: requestData.companyId,
            monthEndDebitAmount: monthEndDebitAmount,
            monthEndCreditAmount: monthEndCreditAmount,
            netChangeAmount: netChangeAmount,
        };
        return processedData;
    }
    catch (error) {
        console.error('❌ Error processing Trial Balance row:', {
            error: error instanceof Error ? error.message : 'Unknown error',
            rowData: row,
        });
        return null;
    }
}
function parseAmount(amountString) {
    if (!amountString || amountString.trim() === '') {
        return null;
    }
    let cleanAmount = amountString.replace(/,/g, '').trim();
    if (cleanAmount.startsWith('(') && cleanAmount.endsWith(')')) {
        cleanAmount = '-' + cleanAmount.slice(1, -1);
    }
    const parsed = parseFloat(cleanAmount);
    return isNaN(parsed) ? null : parsed;
}
function validateTrialBalanceReport(report) {
    if (!report) {
        console.error('❌ Trial Balance report is null or undefined');
        return false;
    }
    if (!report.ReportName || !report.ReportName.toLowerCase().includes('trial')) {
        console.error('❌ Invalid report type, expected Trial Balance report');
        return false;
    }
    if (!Array.isArray(report.Rows)) {
        console.error('❌ Trial Balance report missing Rows array');
        return false;
    }
    return true;
}
//# sourceMappingURL=extractTrialBalanceData.js.map
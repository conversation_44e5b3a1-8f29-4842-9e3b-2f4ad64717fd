export declare enum SyncStatus {
    PENDING = "PENDING",
    IN_PROGRESS = "IN_PROGRESS",
    SUCCESS = "SUCCESS",
    WARNING = "WARNING",
    ERROR = "ERROR",
    RETRYING = "RETRYING",
    CANCELLED = "CANCELLED"
}
export interface SyncLogData {
    requestId?: string;
    entity: string;
    integration: string;
    apiEndpoint?: string;
    method?: string;
    companyId: string;
    userId?: string;
    requestPayload?: any;
    maxRetries?: number;
}
export interface SyncLogUpdate {
    Status?: SyncStatus;
    Message?: string;
    Duration?: string;
    ResponsePayload?: any;
    ErrorDetails?: any;
    RetryCount?: number;
    LastRetryAt?: Date;
    NextRetryAt?: Date;
    CompletedAt?: Date;
    ApiEndpoint?: string;
    Method?: string;
}
export declare function createSyncLog(logData: SyncLogData): Promise<string>;
export declare function updateSyncLog(syncLogId: string, updateData: SyncLogUpdate): Promise<void>;
export declare function markSyncStarted(syncLogId: string, apiEndpoint?: string, method?: string): Promise<void>;
export declare function markSyncSuccess(syncLogId: string, duration: number, message?: string, responsePayload?: any): Promise<void>;
export declare function markSyncFailed(syncLogId: string, duration: number, error: any, retryCount?: number): Promise<void>;
export declare function markSyncForRetry(syncLogId: string, retryCount: number, nextRetryAt: Date, error?: any): Promise<void>;
export declare function getSyncLogs(companyId: string, entity?: string, status?: SyncStatus, limit?: number): Promise<{
    Id: string;
    Status: import(".prisma/client").$Enums.SyncStatus;
    Duration: string | null;
    CreatedAt: Date;
    RequestId: string | null;
    Entity: string;
    Integration: string;
    Message: string | null;
    RetryCount: number;
    StartedAt: Date;
    CompletedAt: Date | null;
}[]>;
//# sourceMappingURL=syncLogService.d.ts.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const environment_1 = require("../config/environment");
const axiosInstance = axios_1.default.create({
    timeout: environment_1.PRODUCTION_CONFIG.API_TIMEOUT_MS,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});
axiosInstance.interceptors.request.use((config) => {
    console.log(`🌐 Making ${config.method?.toUpperCase()} request to: ${config.url}`);
    return config;
}, (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
});
axiosInstance.interceptors.response.use((response) => {
    console.log(`✅ Response received: ${response.status} ${response.statusText}`);
    return response;
}, (error) => {
    if (error.response) {
        console.error(`❌ API Error: ${error.response.status} ${error.response.statusText}`, {
            url: error.config?.url,
            data: error.response.data,
        });
    }
    else if (error.request) {
        console.error('❌ Network Error: No response received', {
            url: error.config?.url,
            timeout: error.code === 'ECONNABORTED',
        });
    }
    else {
        console.error('❌ Request Setup Error:', error.message);
    }
    return Promise.reject(error);
});
exports.default = axiosInstance;
//# sourceMappingURL=axiosInstance.js.map
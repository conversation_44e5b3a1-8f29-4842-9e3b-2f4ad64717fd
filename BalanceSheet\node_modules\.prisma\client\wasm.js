
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  Id: 'Id',
  Name: 'Name',
  Email: 'Email',
  Password: 'Password',
  IsActive: 'IsActive',
  IsVerified: 'IsVerified',
  RefreshToken: 'RefreshToken',
  TokenExpiry: 'TokenExpiry',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  LastLoginAt: 'LastLoginAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  Id: 'Id',
  UserId: 'UserId',
  Name: 'Name',
  XeroTenantId: 'XeroTenantId',
  XeroAccessToken: 'XeroAccessToken',
  XeroRefreshToken: 'XeroRefreshToken',
  XeroTokenExpiry: 'XeroTokenExpiry',
  XeroRefreshTokenExpiry: 'XeroRefreshTokenExpiry',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  FinancialYearEnd: 'FinancialYearEnd',
  LastSyncDate: 'LastSyncDate',
  NextSyncDate: 'NextSyncDate',
  ConnectionStatus: 'ConnectionStatus'
};

exports.Prisma.AccountScalarFieldEnum = {
  Id: 'Id',
  BankAccountType: 'BankAccountType',
  BankAccountNumber: 'BankAccountNumber',
  Code: 'Code',
  AccountClassTypes: 'AccountClassTypes',
  Type: 'Type',
  Name: 'Name',
  Description: 'Description',
  ReportingCode: 'ReportingCode',
  ReportingCodeName: 'ReportingCodeName',
  CurrencyCode: 'CurrencyCode',
  TaxType: 'TaxType',
  SystemAccount: 'SystemAccount',
  Status: 'Status',
  EnablePaymentsToAccount: 'EnablePaymentsToAccount',
  ShowInExpenseClaims: 'ShowInExpenseClaims',
  UpdateUtcDate: 'UpdateUtcDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.BankTransactionScalarFieldEnum = {
  BankTransactionID: 'BankTransactionID',
  Type: 'Type',
  AccountID: 'AccountID',
  BankAccountNumber: 'BankAccountNumber',
  Code: 'Code',
  BankAccountName: 'BankAccountName',
  ContactID: 'ContactID',
  ContactName: 'ContactName',
  ContactFirstName: 'ContactFirstName',
  ContactLastName: 'ContactLastName',
  CurrencyCode: 'CurrencyCode',
  Date: 'Date',
  DueDate: 'DueDate',
  FullyPaidOnDate: 'FullyPaidOnDate',
  IsReconciled: 'IsReconciled',
  LineAmountTypes: 'LineAmountTypes',
  Reference: 'Reference',
  Status: 'Status',
  SubTotal: 'SubTotal',
  TotalTax: 'TotalTax',
  Total: 'Total',
  TotalMovement: 'TotalMovement',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId',
  CurrencyRate: 'CurrencyRate'
};

exports.Prisma.BankTransactionLineScalarFieldEnum = {
  Id: 'Id',
  BankTransactionId: 'BankTransactionId',
  LineItemId: 'LineItemId',
  ContactId: 'ContactId',
  ContactName: 'ContactName',
  Date: 'Date',
  Reference: 'Reference',
  Description: 'Description',
  Quantity: 'Quantity',
  UnitAmount: 'UnitAmount',
  AccountCode: 'AccountCode',
  TaxAmount: 'TaxAmount',
  LineAmount: 'LineAmount',
  TaxType: 'TaxType',
  TrackingCategory1: 'TrackingCategory1',
  TrackingCategory1Value: 'TrackingCategory1Value',
  TrackingCategory2: 'TrackingCategory2',
  TrackingCategory2Value: 'TrackingCategory2Value',
  CurrencyCode: 'CurrencyCode',
  CurrencyRate: 'CurrencyRate',
  CompanyId: 'CompanyId'
};

exports.Prisma.BankTransferScalarFieldEnum = {
  BankTransferID: 'BankTransferID',
  FromBankAccountAccountID: 'FromBankAccountAccountID',
  FromBankAccountName: 'FromBankAccountName',
  ToBankAccountAccountID: 'ToBankAccountAccountID',
  ToBankAccountName: 'ToBankAccountName',
  Amount: 'Amount',
  FromBankTransactionID: 'FromBankTransactionID',
  ToBankTransactionID: 'ToBankTransactionID',
  FromIsReconciled: 'FromIsReconciled',
  ToIsReconciled: 'ToIsReconciled',
  CurrencyRate: 'CurrencyRate',
  Reference: 'Reference',
  CreatedDateUTC: 'CreatedDateUTC',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.BudgetScalarFieldEnum = {
  BudgetID: 'BudgetID',
  BudgetType: 'BudgetType',
  Description: 'Description',
  Trackings: 'Trackings',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.ContactScalarFieldEnum = {
  ContactID: 'ContactID',
  ContactNumber: 'ContactNumber',
  ContactStatus: 'ContactStatus',
  AccountNumber: 'AccountNumber',
  Name: 'Name',
  FirstName: 'FirstName',
  LastName: 'LastName',
  EmailAddress: 'EmailAddress',
  SkypeUserName: 'SkypeUserName',
  DefaultCurrency: 'DefaultCurrency',
  BankAccountDetails: 'BankAccountDetails',
  TaxNumber: 'TaxNumber',
  AccountsReceivableTaxType: 'AccountsReceivableTaxType',
  AccountsPayableTaxType: 'AccountsPayableTaxType',
  IsSupplier: 'IsSupplier',
  IsCustomer: 'IsCustomer',
  PurchasesDefaultAccountCode: 'PurchasesDefaultAccountCode',
  SalesDefaultAccountCode: 'SalesDefaultAccountCode',
  BatchPaymentsBankAccountNumber: 'BatchPaymentsBankAccountNumber',
  BatchPaymentsBankAccountName: 'BatchPaymentsBankAccountName',
  BatchPaymentsDetails: 'BatchPaymentsDetails',
  AccountsReceivableOutstanding: 'AccountsReceivableOutstanding',
  AccountsReceivableOverdue: 'AccountsReceivableOverdue',
  AccountsPayableOutstanding: 'AccountsPayableOutstanding',
  AccountsPayableOverdue1: 'AccountsPayableOverdue1',
  PhoneDefaultNumber: 'PhoneDefaultNumber',
  PhoneDefaultAreaCode: 'PhoneDefaultAreaCode',
  PhoneDefaultCountryCode: 'PhoneDefaultCountryCode',
  PhoneDDINumber: 'PhoneDDINumber',
  PhoneDDIAreaCode: 'PhoneDDIAreaCode',
  PhoneDDICountryCode: 'PhoneDDICountryCode',
  PhoneMobileNumber: 'PhoneMobileNumber',
  PhoneMobileAreaCode: 'PhoneMobileAreaCode',
  PhoneMobileCountryCode: 'PhoneMobileCountryCode',
  PhoneFaxNumber: 'PhoneFaxNumber',
  PhoneFaxAreaCode: 'PhoneFaxAreaCode',
  PhoneFaxCountryCode: 'PhoneFaxCountryCode',
  MailingAddressAttensionTo: 'MailingAddressAttensionTo',
  MailingAddressAddressLine1: 'MailingAddressAddressLine1',
  MailingAddressAddressLine2: 'MailingAddressAddressLine2',
  MailingAddressAddressLine3: 'MailingAddressAddressLine3',
  MailingAddressAddressLine4: 'MailingAddressAddressLine4',
  MailingAddressCity: 'MailingAddressCity',
  MailingAddressCountry: 'MailingAddressCountry',
  MailingAddressPostalCode: 'MailingAddressPostalCode',
  MailingAddressRegion: 'MailingAddressRegion',
  StreetAttensionTo: 'StreetAttensionTo',
  StreetAddressLine1: 'StreetAddressLine1',
  StreetAddressLine2: 'StreetAddressLine2',
  StreetAddressLine3: 'StreetAddressLine3',
  StreetAddressLine4: 'StreetAddressLine4',
  StreetCity: 'StreetCity',
  StreetCountry: 'StreetCountry',
  StreetPostalCode: 'StreetPostalCode',
  StreetRegion: 'StreetRegion',
  ContactGroups: 'ContactGroups',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.CreditNoteScalarFieldEnum = {
  CreditNoteID: 'CreditNoteID',
  Type: 'Type',
  CreditNoteNumber: 'CreditNoteNumber',
  ContactId: 'ContactId',
  ContactName: 'ContactName',
  ContactFirstName: 'ContactFirstName',
  ContactLastName: 'ContactLastName',
  Date: 'Date',
  DueDate: 'DueDate',
  FullyPaidOnDate: 'FullyPaidOnDate',
  LineAmountType: 'LineAmountType',
  Reference: 'Reference',
  Status: 'Status',
  AmountDue: 'AmountDue',
  AmountPaid: 'AmountPaid',
  AmountAllocated: 'AmountAllocated',
  AmountDueToDate: 'AmountDueToDate',
  AmountPaidToDate: 'AmountPaidToDate',
  AmountAllocatedToDate: 'AmountAllocatedToDate',
  RemainingCredit: 'RemainingCredit',
  CurrencyCode: 'CurrencyCode',
  CurrencyRate: 'CurrencyRate',
  SubTotal: 'SubTotal',
  TotalTax: 'TotalTax',
  Total: 'Total',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.CreditNoteLineScalarFieldEnum = {
  Id: 'Id',
  CreditNoteId: 'CreditNoteId',
  LineItemId: 'LineItemId',
  CreditNoteNumber: 'CreditNoteNumber',
  ContactId: 'ContactId',
  ContactName: 'ContactName',
  Date: 'Date',
  Reference: 'Reference',
  ItemCode: 'ItemCode',
  Description: 'Description',
  Quantity: 'Quantity',
  UnitAmount: 'UnitAmount',
  UnitCost: 'UnitCost',
  AccountCode: 'AccountCode',
  TaxAmount: 'TaxAmount',
  TaxType: 'TaxType',
  LineAmount: 'LineAmount',
  TrackingCategory1: 'TrackingCategory1',
  TrackingCategory1Value: 'TrackingCategory1Value',
  TrackingCategory2: 'TrackingCategory2',
  TrackingCategory2Value: 'TrackingCategory2Value',
  CurrencyCode: 'CurrencyCode',
  CurrencyRate: 'CurrencyRate',
  CompanyId: 'CompanyId'
};

exports.Prisma.CurrencyScalarFieldEnum = {
  Code: 'Code',
  Description: 'Description',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.EmployeeScalarFieldEnum = {
  EmployeeID: 'EmployeeID',
  Status: 'Status',
  FirstName: 'FirstName',
  LastName: 'LastName',
  ExternalLinkUrl: 'ExternalLinkUrl',
  ExternalLinkDescription: 'ExternalLinkDescription',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.ExpenseClaimScalarFieldEnum = {
  ExpenseClaimID: 'ExpenseClaimID',
  Status: 'Status',
  UserID: 'UserID',
  EmailAddress: 'EmailAddress',
  FirstName: 'FirstName',
  LastName: 'LastName',
  UserUpdatedDateUTC: 'UserUpdatedDateUTC',
  IsSubscriber: 'IsSubscriber',
  OrganisationRole: 'OrganisationRole',
  Total: 'Total',
  AmountDue: 'AmountDue',
  AmountPaid: 'AmountPaid',
  PaymentDueDate: 'PaymentDueDate',
  ReportingDate: 'ReportingDate',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  InvoiceID: 'InvoiceID',
  Type: 'Type',
  InvoiceNumber: 'InvoiceNumber',
  ContactID: 'ContactID',
  ContactName: 'ContactName',
  FirstName: 'FirstName',
  LastName: 'LastName',
  Date: 'Date',
  DueDate: 'DueDate',
  FullyPaidOnDate: 'FullyPaidOnDate',
  ExpectedPaymentDate: 'ExpectedPaymentDate',
  PlannedPaymentDate: 'PlannedPaymentDate',
  LineAmountTypes: 'LineAmountTypes',
  Reference: 'Reference',
  Status: 'Status',
  HasAttachments: 'HasAttachments',
  SentToContact: 'SentToContact',
  BrandingThemeID: 'BrandingThemeID',
  BrandingThemeName: 'BrandingThemeName',
  AmountDue: 'AmountDue',
  AmountPaid: 'AmountPaid',
  AmountCredited: 'AmountCredited',
  AmountPaidToDate: 'AmountPaidToDate',
  AmountDueToDate: 'AmountDueToDate',
  AmountCreditedToDate: 'AmountCreditedToDate',
  CurrencyCode: 'CurrencyCode',
  CurrencyRate: 'CurrencyRate',
  SubTotal: 'SubTotal',
  TotalDiscount: 'TotalDiscount',
  TotalTax: 'TotalTax',
  Total: 'Total',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.InvoiceLineScalarFieldEnum = {
  Id: 'Id',
  InvoiceID: 'InvoiceID',
  InvoiceNumber: 'InvoiceNumber',
  ContactID: 'ContactID',
  ContactName: 'ContactName',
  Date: 'Date',
  Reference: 'Reference',
  LineItemID: 'LineItemID',
  ItemCode: 'ItemCode',
  Description: 'Description',
  Quantity: 'Quantity',
  UnitAmount: 'UnitAmount',
  UnitCost: 'UnitCost',
  DiscountRate: 'DiscountRate',
  AccountCode: 'AccountCode',
  AccountID: 'AccountID',
  TaxAmount: 'TaxAmount',
  TaxType: 'TaxType',
  LineAmountTypes: 'LineAmountTypes',
  LineAmount: 'LineAmount',
  LineAmountIncl: 'LineAmountIncl',
  LineAmountExcl: 'LineAmountExcl',
  CompanyId: 'CompanyId',
  TrackingCategory1: 'TrackingCategory1',
  TrackingCategory1Value: 'TrackingCategory1Value',
  TrackingCategory2: 'TrackingCategory2',
  TrackingCategory2Value: 'TrackingCategory2Value',
  CurrencyCode: 'CurrencyCode',
  CurrencyRate: 'CurrencyRate'
};

exports.Prisma.ItemScalarFieldEnum = {
  ItemID: 'ItemID',
  Name: 'Name',
  Code: 'Code',
  Description: 'Description',
  PurchaseDescription: 'PurchaseDescription',
  IsPurchased: 'IsPurchased',
  IsSold: 'IsSold',
  IsTrackedAsInventory: 'IsTrackedAsInventory',
  TotalCostPool: 'TotalCostPool',
  QuantityOnHand: 'QuantityOnHand',
  InventoryAssetAccountCode: 'InventoryAssetAccountCode',
  PurchaseAccountCode: 'PurchaseAccountCode',
  PurchaseTaxType: 'PurchaseTaxType',
  PurchaseUnitPrice: 'PurchaseUnitPrice',
  COGSAccountCode: 'COGSAccountCode',
  SalesTaxType: 'SalesTaxType',
  SalesAccountCode: 'SalesAccountCode',
  SalesUnitPrice: 'SalesUnitPrice',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.JournalScalarFieldEnum = {
  JournalID: 'JournalID',
  JournalNumber: 'JournalNumber',
  CreatedDateUTC: 'CreatedDateUTC',
  JournalDate: 'JournalDate',
  Reference: 'Reference',
  SourceType: 'SourceType',
  SourceID: 'SourceID',
  TotalGrossAmount: 'TotalGrossAmount',
  TotalNetAmount: 'TotalNetAmount',
  TotalTaxAmount: 'TotalTaxAmount',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.JournalLineScalarFieldEnum = {
  Id: 'Id',
  JournalID: 'JournalID',
  JournalNumber: 'JournalNumber',
  JournalDate: 'JournalDate',
  Reference: 'Reference',
  JournalLineID: 'JournalLineID',
  SourceType: 'SourceType',
  SourceID: 'SourceID',
  AccountID: 'AccountID',
  AccountCode: 'AccountCode',
  AccountType: 'AccountType',
  AccountName: 'AccountName',
  Description: 'Description',
  NetAmount: 'NetAmount',
  GrossAmount: 'GrossAmount',
  Debit: 'Debit',
  Credit: 'Credit',
  TaxAmount: 'TaxAmount',
  TaxType: 'TaxType',
  TaxName: 'TaxName',
  CompanyId: 'CompanyId',
  TrackingCategory1: 'TrackingCategory1',
  TrackingCategory1Value: 'TrackingCategory1Value',
  TrackingCategory2: 'TrackingCategory2',
  TrackingCategory2Value: 'TrackingCategory2Value',
  Status: 'Status'
};

exports.Prisma.ManualJournalScalarFieldEnum = {
  ManualJournalID: 'ManualJournalID',
  Date: 'Date',
  Status: 'Status',
  LineAmountTypes: 'LineAmountTypes',
  Narration: 'Narration',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.ManualJournalLineScalarFieldEnum = {
  Id: 'Id',
  ManualJournalID: 'ManualJournalID',
  Description: 'Description',
  TaxType: 'TaxType',
  TaxAmount: 'TaxAmount',
  Debit: 'Debit',
  Credit: 'Credit',
  AccountCode: 'AccountCode',
  AccountID: 'AccountID',
  Region: 'Region',
  TrackingCategory1: 'TrackingCategory1',
  TrackingCategory1Value: 'TrackingCategory1Value',
  TrackingCategory2: 'TrackingCategory2',
  TrackingCategory2Value: 'TrackingCategory2Value',
  IsBlank: 'IsBlank',
  IsRecentUpdatedInTable: 'IsRecentUpdatedInTable',
  CompanyId: 'CompanyId'
};

exports.Prisma.OrganisationScalarFieldEnum = {
  OrganisationID: 'OrganisationID',
  Name: 'Name',
  LegalName: 'LegalName',
  ShortCode: 'ShortCode',
  OrganisationType: 'OrganisationType',
  CountryCode: 'CountryCode',
  BaseCurrency: 'BaseCurrency',
  IsDemoCompany: 'IsDemoCompany',
  FinancialYearEndDay: 'FinancialYearEndDay',
  FinancialYearEndMonth: 'FinancialYearEndMonth',
  EndOfYearLockDate: 'EndOfYearLockDate',
  PeriodLockDate: 'PeriodLockDate',
  PaysTax: 'PaysTax',
  TaxNumber: 'TaxNumber',
  Timezone: 'Timezone',
  Version: 'Version',
  MailingAddressAttensionTo: 'MailingAddressAttensionTo',
  MailingAddressAddressLine1: 'MailingAddressAddressLine1',
  MailingAddressAddressLine2: 'MailingAddressAddressLine2',
  MailingAddressAddressLine3: 'MailingAddressAddressLine3',
  MailingAddressAddressLine4: 'MailingAddressAddressLine4',
  MailingAddressCity: 'MailingAddressCity',
  MailingAddressCountry: 'MailingAddressCountry',
  MailingAddressPostalCode: 'MailingAddressPostalCode',
  MailingAddressRegion: 'MailingAddressRegion',
  StreetAttensionTo: 'StreetAttensionTo',
  StreetAddressLine1: 'StreetAddressLine1',
  StreetAddressLine2: 'StreetAddressLine2',
  StreetAddressLine3: 'StreetAddressLine3',
  StreetAddressLine4: 'StreetAddressLine4',
  StreetCity: 'StreetCity',
  StreetCountry: 'StreetCountry',
  StreetPostalCode: 'StreetPostalCode',
  StreetRegion: 'StreetRegion',
  PhoneDefaultNumber: 'PhoneDefaultNumber',
  PhoneDefaultAreaCode: 'PhoneDefaultAreaCode',
  PhoneDefaultCountryCode: 'PhoneDefaultCountryCode',
  PhoneDDINumber: 'PhoneDDINumber',
  PhoneDDIAreaCode: 'PhoneDDIAreaCode',
  PhoneDDICountryCode: 'PhoneDDICountryCode',
  PhoneMobileNumber: 'PhoneMobileNumber',
  PhoneMobileAreaCode: 'PhoneMobileAreaCode',
  PhoneMobileCountryCode: 'PhoneMobileCountryCode',
  PhoneFaxNumber: 'PhoneFaxNumber',
  PhoneFaxAreaCode: 'PhoneFaxAreaCode',
  PhoneFaxCountryCode: 'PhoneFaxCountryCode',
  PhoneOfficeNumber: 'PhoneOfficeNumber',
  PhoneOfficeAreaCode: 'PhoneOfficeAreaCode',
  PhoneOfficeCountryCode: 'PhoneOfficeCountryCode',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.PaymentScalarFieldEnum = {
  PaymentID: 'PaymentID',
  PaymentType: 'PaymentType',
  Date: 'Date',
  AccountID: 'AccountID',
  BankAccountNumber: 'BankAccountNumber',
  AccountCode: 'AccountCode',
  AccountName: 'AccountName',
  BankAmount: 'BankAmount',
  Amount: 'Amount',
  CurrencyRate: 'CurrencyRate',
  InvoiceID: 'InvoiceID',
  InvoiceNumber: 'InvoiceNumber',
  InvoiceTotal: 'InvoiceTotal',
  Reference: 'Reference',
  Status: 'Status',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.ReceiptScalarFieldEnum = {
  ReceiptsID: 'ReceiptsID',
  ReceiptNumber: 'ReceiptNumber',
  Status: 'Status',
  UserID: 'UserID',
  FirstName: 'FirstName',
  LastName: 'LastName',
  ContactID: 'ContactID',
  ContactName: 'ContactName',
  Date: 'Date',
  Reference: 'Reference',
  LineAmountTypes: 'LineAmountTypes',
  SubTotal: 'SubTotal',
  TotalTax: 'TotalTax',
  Total: 'Total',
  HasAttachments: 'HasAttachments',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.TaxRateScalarFieldEnum = {
  Id: 'Id',
  Name: 'Name',
  TaxType: 'TaxType',
  ReportTaxType: 'ReportTaxType',
  CanApplyToAssets: 'CanApplyToAssets',
  CanApplyToEquity: 'CanApplyToEquity',
  CanApplyToExpenses: 'CanApplyToExpenses',
  CanApplyToLiabilities: 'CanApplyToLiabilities',
  CanApplyToRevenue: 'CanApplyToRevenue',
  DisplayTaxRate: 'DisplayTaxRate',
  EffectiveRate: 'EffectiveRate',
  Status: 'Status',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.TaxRateLineScalarFieldEnum = {
  Id: 'Id',
  TaxRateName: 'TaxRateName',
  TaxComponentsName: 'TaxComponentsName',
  Rate: 'Rate',
  IsCompound: 'IsCompound',
  IsNonRecoverable: 'IsNonRecoverable',
  CompanyId: 'CompanyId',
  IsRecentUpdatedInTable: 'IsRecentUpdatedInTable'
};

exports.Prisma.TrackingCategoryScalarFieldEnum = {
  TrackingCategoryID: 'TrackingCategoryID',
  CategoryName: 'CategoryName',
  TotalOptions: 'TotalOptions',
  Status: 'Status',
  UpdateUTCDate: 'UpdateUTCDate',
  CompanyId: 'CompanyId'
};

exports.Prisma.TrackingCategoryLineScalarFieldEnum = {
  Id: 'Id',
  TrackingCategoryID: 'TrackingCategoryID',
  TrackingOptionID: 'TrackingOptionID',
  Status: 'Status',
  Name: 'Name',
  CompanyId: 'CompanyId',
  IsRecentUpdatedInTable: 'IsRecentUpdatedInTable'
};

exports.Prisma.ApiLogScalarFieldEnum = {
  Id: 'Id',
  UserId: 'UserId',
  CompanyId: 'CompanyId',
  Method: 'Method',
  ApiUrl: 'ApiUrl',
  Status: 'Status',
  IntegrationName: 'IntegrationName',
  Duration: 'Duration',
  ApiName: 'ApiName',
  ApiRequest: 'ApiRequest',
  ApiResponse: 'ApiResponse',
  IsActive: 'IsActive',
  CreatedAt: 'CreatedAt'
};

exports.Prisma.SyncLogScalarFieldEnum = {
  Id: 'Id',
  RequestId: 'RequestId',
  Entity: 'Entity',
  Integration: 'Integration',
  ApiEndpoint: 'ApiEndpoint',
  Method: 'Method',
  Status: 'Status',
  Message: 'Message',
  Duration: 'Duration',
  RetryCount: 'RetryCount',
  MaxRetries: 'MaxRetries',
  LastRetryAt: 'LastRetryAt',
  NextRetryAt: 'NextRetryAt',
  StartedAt: 'StartedAt',
  CompletedAt: 'CompletedAt',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  CompanyId: 'CompanyId',
  UserId: 'UserId',
  RequestPayload: 'RequestPayload',
  ResponsePayload: 'ResponsePayload',
  ErrorDetails: 'ErrorDetails'
};

exports.Prisma.ProfitLossTrackingScalarFieldEnum = {
  Id: 'Id',
  Year: 'Year',
  Month: 'Month',
  AccountId: 'AccountId',
  AccountName: 'AccountName',
  Amount: 'Amount',
  TrackingCategoryId1: 'TrackingCategoryId1',
  TrackingCategoryId2: 'TrackingCategoryId2',
  CompanyId: 'CompanyId'
};

exports.Prisma.ProfitLossScalarFieldEnum = {
  Id: 'Id',
  Year: 'Year',
  Month: 'Month',
  AccountId: 'AccountId',
  AccountName: 'AccountName',
  Amount: 'Amount',
  CompanyId: 'CompanyId'
};

exports.Prisma.BalanceSheetTrackingScalarFieldEnum = {
  Id: 'Id',
  Year: 'Year',
  Month: 'Month',
  AccountId: 'AccountId',
  AccountName: 'AccountName',
  Amount: 'Amount',
  TrackingCategoryId1: 'TrackingCategoryId1',
  TrackingCategoryId2: 'TrackingCategoryId2',
  CompanyId: 'CompanyId'
};

exports.Prisma.BalanceSheetScalarFieldEnum = {
  Id: 'Id',
  Year: 'Year',
  Month: 'Month',
  AccountId: 'AccountId',
  AccountName: 'AccountName',
  Amount: 'Amount',
  CompanyId: 'CompanyId'
};

exports.Prisma.TrialBalanceScalarFieldEnum = {
  Id: 'Id',
  Year: 'Year',
  Month: 'Month',
  AccountId: 'AccountId',
  AccountName: 'AccountName',
  Amount: 'Amount',
  CompanyId: 'CompanyId',
  monthEndDebitAmount: 'monthEndDebitAmount',
  monthEndCreditAmount: 'monthEndCreditAmount',
  netChangeAmount: 'netChangeAmount'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.ConnectionStatus = exports.$Enums.ConnectionStatus = {
  ACTIVE: 'ACTIVE',
  EXPIRED: 'EXPIRED',
  DISCONNECTED: 'DISCONNECTED',
  PENDING: 'PENDING'
};

exports.SyncStatus = exports.$Enums.SyncStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  SUCCESS: 'SUCCESS',
  WARNING: 'WARNING',
  ERROR: 'ERROR',
  RETRYING: 'RETRYING',
  CANCELLED: 'CANCELLED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Company: 'Company',
  Account: 'Account',
  BankTransaction: 'BankTransaction',
  BankTransactionLine: 'BankTransactionLine',
  BankTransfer: 'BankTransfer',
  Budget: 'Budget',
  Contact: 'Contact',
  CreditNote: 'CreditNote',
  CreditNoteLine: 'CreditNoteLine',
  Currency: 'Currency',
  Employee: 'Employee',
  ExpenseClaim: 'ExpenseClaim',
  Invoice: 'Invoice',
  InvoiceLine: 'InvoiceLine',
  Item: 'Item',
  Journal: 'Journal',
  JournalLine: 'JournalLine',
  ManualJournal: 'ManualJournal',
  ManualJournalLine: 'ManualJournalLine',
  Organisation: 'Organisation',
  Payment: 'Payment',
  Receipt: 'Receipt',
  TaxRate: 'TaxRate',
  TaxRateLine: 'TaxRateLine',
  TrackingCategory: 'TrackingCategory',
  TrackingCategoryLine: 'TrackingCategoryLine',
  ApiLog: 'ApiLog',
  SyncLog: 'SyncLog',
  ProfitLossTracking: 'ProfitLossTracking',
  ProfitLoss: 'ProfitLoss',
  BalanceSheetTracking: 'BalanceSheetTracking',
  BalanceSheet: 'BalanceSheet',
  TrialBalance: 'TrialBalance'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)

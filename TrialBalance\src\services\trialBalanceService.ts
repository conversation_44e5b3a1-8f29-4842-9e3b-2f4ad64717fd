/**
 * Trial Balance Service
 * 
 * Main service for processing Trial Balance data synchronization from Xero API
 */

import moment from 'moment';
import { Context } from 'aws-lambda';
import { PrismaClient } from '@prisma/client';
import {
    XeroRequestData,
    ProcessedTrialBalanceData,
    ValidationError,
    MonthInfo,
    CompanyIntegration,
    SyncPeriod
} from '../types';
import { refreshXeroToken } from './refreshTokenService';
import { getXeroConfig } from '../config/environment';
import axios from '../utils/axiosInstance';
import { extractTrialBalanceData, validateTrialBalanceReport } from './extractTrialBalanceData';
import { PRODUCTION_CONFIG } from '../config/environment';
import pLimit from 'p-limit';
import {
    createSyncLog,
    markSyncStarted,
    markSyncSuccess,
    markSyncFailed,
    SyncLogData
} from './syncLogService';
import {
    logSuccessfulApiCall,
    logFailedApiCall
} from './apiLogService';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

let prisma: PrismaClient | null = null;

/**
 * Get or create Prisma client instance
 * @returns {PrismaClient} Singleton Prisma client instance
 */
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            // Only log errors and warnings in production
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        // Handle offline development environment
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

/**
 * Main Processing Function for Xero Trial Balance Synchronization
 * 
 * Orchestrates the complete sync process:
 * 1. Validates request data and retrieves company information
 * 2. Ensures valid Xero OAuth token
 * 3. Determines sync period based on existing data
 * 4. Processes months in parallel with rate limiting
 * 5. Stores data in TrialBalance table
 * 
 * @param requestData - Request containing companyId and tenantId
 * @param _context - AWS Lambda context (unused but required)
 * @throws ValidationError When request data is invalid
 * @throws Error When company not found or processing fails
 */
export async function processTrialBalanceRequest(
    requestData: XeroRequestData,
    _context: Context
): Promise<void> {
    const startTime = Date.now();
    console.log(`🚀 Starting Trial Balance sync for company: ${requestData.companyId}`);

    // Create sync log entry
    const syncLogData: SyncLogData = {
        entity: 'TrialBalance',
        integration: 'Xero',
        companyId: requestData.companyId,
        requestPayload: requestData,
        maxRetries: 3,
    };

    const syncLogId = await createSyncLog(syncLogData);

    try {
        // Mark sync as started
        await markSyncStarted(syncLogId, 'Reports/TrialBalance', 'GET');

        // Step 1: Validate request and get company data
        const integration = await validateAndGetCompany(requestData.companyId);
        console.log(`✅ Company validation successful: ${integration.Id}`);

        // Step 2: Ensure valid access token
        await refreshXeroToken(integration.Id);
        console.log('✅ Access token validated/refreshed');

        // Step 3: Determine sync period
        const syncPeriod = await determineSyncPeriod(integration.Id, requestData);
        console.log(`📅 Sync period determined: ${syncPeriod.monthsToSync} months (${syncPeriod.isInitialSync ? 'Initial' : 'Regular'} sync)`);

        // Step 4: Generate month list for processing
        const monthsToProcess = generateMonthList(syncPeriod.startDate, syncPeriod.endDate);
        console.log(`📋 Generated ${monthsToProcess.length} months to process`);

        // Step 5: Process months with rate limiting
        const concurrencyLimit = syncPeriod.isInitialSync ? 1 : PRODUCTION_CONFIG.MAX_CONCURRENT_REQUESTS;
        const limit = pLimit(concurrencyLimit);

        console.log(`⚡ Processing with concurrency limit: ${concurrencyLimit}`);

        const processingPromises = monthsToProcess.map(month =>
            limit(() => processTrialBalanceMonth(integration, requestData, month))
        );

        await Promise.all(processingPromises);

        const totalTime = Date.now() - startTime;
        console.log(`🎉 Trial Balance sync completed successfully in ${totalTime}ms for company: ${requestData.companyId}`);
        console.log(`📊 Processed ${monthsToProcess.length} months with ${concurrencyLimit} concurrent requests`);

        // Mark sync as successful
        await markSyncSuccess(
            syncLogId,
            totalTime,
            `Successfully processed ${monthsToProcess.length} months of Trial Balance data`,
            { monthsProcessed: monthsToProcess.length, concurrencyLimit }
        );

    } catch (error: any) {
        const totalTime = Date.now() - startTime;
        console.error(`❌ Trial Balance sync failed after ${totalTime}ms:`, {
            companyId: requestData.companyId,
            error: error.message,
            stack: error.stack,
        });

        // Mark sync as failed
        await markSyncFailed(syncLogId, totalTime, error);

        throw error;
    } finally {
        await getPrismaClient().$disconnect();
    }
}

/**
 * Validate request data and retrieve company integration information
 */
async function validateAndGetCompany(companyId: string): Promise<CompanyIntegration> {
    console.log(`🔍 Validating company: ${companyId}`);

    if (!companyId || typeof companyId !== 'string') {
        throw new ValidationError('Invalid companyId provided');
    }

    const company = await getPrismaClient().company.findFirst({
        where: { Id: companyId },
        select: {
            Id: true,
            XeroAccessToken: true,
            XeroTenantId: true,
            XeroTokenExpiry: true,
            XeroRefreshToken: true,
            XeroRefreshTokenExpiry: true,
        },
    });

    if (!company) {
        throw new Error(`Company not found: ${companyId}`);
    }

    if (!company.XeroAccessToken || !company.XeroTenantId) {
        throw new Error(`Company ${companyId} is not connected to Xero. Please complete Xero integration first.`);
    }

    return company;
}

/**
 * Determine sync period based on existing data and request parameters
 */
async function determineSyncPeriod(companyId: string, requestData: XeroRequestData): Promise<SyncPeriod> {
    console.log('📅 Determining sync period...');

    // If specific dates provided in request, use them
    if (requestData.startDate && requestData.endDate) {
        const startDate = new Date(requestData.startDate);
        const endDate = new Date(requestData.endDate);
        const monthsDiff = moment(endDate).diff(moment(startDate), 'months') + 1;

        return {
            startDate,
            endDate,
            monthsToSync: monthsDiff,
            isInitialSync: false,
        };
    }

    // Check for existing data to determine if this is initial sync
    const existingDataCount = await getPrismaClient().trialBalance.count({
        where: { CompanyId: companyId },
    });

    const isInitialSync = existingDataCount === 0;
    const monthsToSync = isInitialSync ? 60 : 13; // 5 years vs 13 months

    const endDate = new Date();
    const startDate = moment(endDate).subtract(monthsToSync - 1, 'months').startOf('month').toDate();

    console.log(`📊 Sync strategy: ${isInitialSync ? 'Initial' : 'Regular'} (${monthsToSync} months)`);

    return {
        startDate,
        endDate,
        monthsToSync,
        isInitialSync,
    };
}

/**
 * Generate list of months to process
 */
function generateMonthList(startDate: Date, endDate: Date): MonthInfo[] {
    const months: MonthInfo[] = [];
    const current = moment(startDate).startOf('month');
    const end = moment(endDate).startOf('month');

    while (current.isSameOrBefore(end)) {
        const monthStart = current.clone().startOf('month');
        const monthEnd = current.clone().endOf('month');

        months.push({
            year: current.year(),
            month: current.month() + 1, // moment uses 0-based months
            startDate: monthStart.format('YYYY-MM-DD'),
            endDate: monthEnd.format('YYYY-MM-DD'),
        });

        current.add(1, 'month');
    }

    return months;
}

/**
 * Process Trial Balance data for a specific month
 */
async function processTrialBalanceMonth(
    integration: CompanyIntegration,
    requestData: XeroRequestData,
    month: MonthInfo
): Promise<void> {
    const monthKey = `${month.year}-${month.month.toString().padStart(2, '0')}`;

    try {
        console.log(`📊 Processing Trial Balance for ${monthKey} (${month.endDate})`);

        // Validate integration data
        if (!integration.XeroAccessToken || !integration.XeroTenantId) {
            throw new Error(`Missing Xero credentials for company: ${integration.Id}`);
        }

        // Prepare monthly request data
        const monthlyRequestData: XeroRequestData = {
            ...requestData,
            startDate: month.startDate,
            endDate: month.endDate,
        };

        // Fetch Trial Balance data from Xero API
        console.log(`🔍 Fetching Trial Balance data for ${monthKey}`);
        const trialBalanceData = await getTrialBalance(
            integration.XeroAccessToken,
            integration.XeroTenantId,
            monthlyRequestData
        );

        // Store data in database
        await storeTrialBalanceData(
            integration.Id,
            month,
            trialBalanceData,
            monthKey
        );

        console.log(`✅ Successfully processed Trial Balance for ${monthKey}`);

    } catch (error: any) {
        console.error(`❌ Failed to process Trial Balance for ${monthKey}:`, {
            error: error.message,
            companyId: integration.Id,
            month: monthKey,
        });
        throw new Error(`Trial Balance processing failed for ${monthKey}: ${error.message}`);
    }
}

/**
 * Fetch Trial Balance data from Xero API
 */
async function getTrialBalance(
    accessToken: string,
    tenantId: string,
    requestData: XeroRequestData
): Promise<ProcessedTrialBalanceData[]> {
    const startTime = Date.now();

    try {
        const { baseUrl } = getXeroConfig();
        const url = `${baseUrl}Reports/TrialBalance?date=${requestData.endDate}`;

        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`);

        const response = await axios.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'TrialBalanceSync/1.0.0',
            },
            timeout: PRODUCTION_CONFIG.API_TIMEOUT_MS,
        });

        const requestTime = Date.now() - startTime;
        console.log(`✅ Trial Balance API call completed in ${requestTime}ms`);

        // Log successful API call
        await logSuccessfulApiCall(
            requestData.companyId,
            'GET',
            url,
            requestTime,
            'TrialBalance',
            { date: requestData.endDate, tenantId: tenantId.substring(0, 8) + '...' },
            { recordCount: response.data?.Reports?.[0]?.Rows?.length || 0 }
        );

        // Validate response structure
        const reportData = response?.data;
        if (!reportData) {
            throw new Error('Empty response received from Xero API');
        }

        if (!reportData.Reports || !Array.isArray(reportData.Reports) || reportData.Reports.length === 0) {
            throw new Error('No reports found in Xero API response');
        }

        if (!reportData.Reports[0]) {
            throw new Error('Invalid Trial Balance report structure received from Xero');
        }

        // Validate report type
        if (!validateTrialBalanceReport(reportData.Reports[0])) {
            throw new Error('Invalid Trial Balance report received from Xero');
        }

        // Extract date information for processing
        const dateObj = new Date(requestData.endDate!);
        const month = {
            year: dateObj.getFullYear(),
            month: dateObj.getMonth() + 1
        };

        console.log(`🔄 Processing Trial Balance data for ${month.year}-${month.month}`);
        const processedData = extractTrialBalanceData(
            reportData.Reports[0],
            requestData,
            month
        );

        console.log(`✅ Processed ${processedData.length} Trial Balance rows`);
        return processedData;

    } catch (error: any) {
        const requestTime = Date.now() - startTime;
        console.error(`❌ Trial Balance API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            date: requestData.endDate,
            tenantId: tenantId.substring(0, 8) + '...',
        });

        // Log failed API call
        await logFailedApiCall(
            requestData.companyId,
            'GET',
            `${getXeroConfig().baseUrl}Reports/TrialBalance?date=${requestData.endDate}`,
            requestTime,
            'TrialBalance',
            error,
            { date: requestData.endDate, tenantId: tenantId.substring(0, 8) + '...' }
        );

        // Handle specific error types
        if (error.response?.status === 429) {
            const retryAfter = error.response.headers['retry-after'] || 60;
            throw new Error(
                `Xero API rate limit exceeded. Retry after ${retryAfter} seconds. Consider reducing concurrency.`
            );
        }

        if (error.response?.status === 401) {
            throw new Error('Xero API authentication failed. Token may be expired or invalid.');
        }

        if (error.response?.status === 403) {
            throw new Error('Xero API access forbidden. Check tenant permissions and subscription.');
        }

        if (error.response?.status === 404) {
            throw new Error('Trial Balance report not found. Check date parameters and Xero subscription.');
        }

        throw error;
    }
}

/**
 * Store Trial Balance data in database with transaction safety
 *
 * Handles the database storage operations for Trial Balance data with proper
 * transaction management and rollback on failure.
 *
 * @param companyId - Company identifier
 * @param month - Month information
 * @param trialBalanceData - Processed Trial Balance data
 * @param monthKey - Month key for logging
 */
async function storeTrialBalanceData(
    companyId: string,
    month: { year: number; month: number },
    trialBalanceData: ProcessedTrialBalanceData[],
    monthKey: string
): Promise<void> {
    console.log(`💾 Storing Trial Balance data for ${monthKey}...`);

    try {
        // Use transaction to ensure data consistency
        await getPrismaClient().$transaction(async (tx) => {
            // Clear existing data for this month
            if (trialBalanceData && trialBalanceData.length > 0) {
                console.log(`🗑️ Clearing existing Trial Balance data for ${monthKey}...`);
                await tx.trialBalance.deleteMany({
                    where: {
                        CompanyId: companyId,
                        Year: month.year,
                        Month: month.month,
                    },
                });

                console.log(`📝 Inserting ${trialBalanceData.length} Trial Balance rows for ${monthKey}...`);
                await tx.trialBalance.createMany({
                    data: trialBalanceData,
                    skipDuplicates: true,
                });

                console.log(`✅ Stored ${trialBalanceData.length} Trial Balance rows for ${monthKey}`);
            } else {
                console.log(`ℹ️ No Trial Balance data to store for ${monthKey}`);
            }
        });

        console.log(`💾 Database transaction completed successfully for ${monthKey}`);

    } catch (error: any) {
        console.error(`❌ Failed to store Trial Balance data for ${monthKey}:`, {
            error: error.message,
            companyId,
            dataCount: trialBalanceData?.length || 0,
        });
        throw new Error(`Database storage failed for ${monthKey}: ${error.message}`);
    }
}

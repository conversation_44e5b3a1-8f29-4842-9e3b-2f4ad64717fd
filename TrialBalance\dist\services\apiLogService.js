"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logApiCall = logApiCall;
exports.logSuccessfulApiCall = logSuccessfulApiCall;
exports.logFailedApiCall = logFailedApiCall;
exports.getRecentApiLogs = getRecentApiLogs;
const client_1 = require("@prisma/client");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
let prisma = null;
function getPrismaClient() {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'],
            errorFormat: 'pretty',
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new client_1.PrismaClient(config);
    }
    return prisma;
}
async function logApiCall(logData) {
    try {
        console.log(`📝 Logging API call: ${logData.method} ${logData.apiUrl} - Status: ${logData.status}`);
        await getPrismaClient().apiLog.create({
            data: {
                UserId: logData.userId || null,
                CompanyId: logData.companyId,
                Method: logData.method,
                ApiUrl: logData.apiUrl,
                Status: logData.status,
                IntegrationName: logData.integrationName,
                Duration: logData.duration,
                ApiName: logData.apiName,
                ApiRequest: logData.apiRequest || null,
                ApiResponse: logData.apiResponse || null,
                IsActive: true,
                CreatedAt: new Date(),
            },
        });
        console.log(`✅ API call logged successfully for ${logData.apiName}`);
    }
    catch (error) {
        console.error('❌ Failed to log API call:', {
            error: error.message,
            apiName: logData.apiName,
            companyId: logData.companyId,
        });
    }
}
async function logSuccessfulApiCall(companyId, method, apiUrl, duration, apiName, request, response, userId) {
    await logApiCall({
        ...(userId && { userId }),
        companyId,
        method,
        apiUrl,
        status: 'SUCCESS',
        integrationName: 'Xero',
        duration: `${duration}ms`,
        apiName,
        apiRequest: request,
        apiResponse: response,
    });
}
async function logFailedApiCall(companyId, method, apiUrl, duration, apiName, error, request, userId) {
    await logApiCall({
        ...(userId && { userId }),
        companyId,
        method,
        apiUrl,
        status: 'ERROR',
        integrationName: 'Xero',
        duration: `${duration}ms`,
        apiName,
        apiRequest: request,
        apiResponse: {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
        },
    });
}
async function getRecentApiLogs(companyId, limit = 50) {
    try {
        return await getPrismaClient().apiLog.findMany({
            where: {
                CompanyId: companyId,
                IsActive: true,
            },
            orderBy: {
                CreatedAt: 'desc',
            },
            take: limit,
            select: {
                Id: true,
                Method: true,
                ApiUrl: true,
                Status: true,
                IntegrationName: true,
                Duration: true,
                ApiName: true,
                CreatedAt: true,
            },
        });
    }
    catch (error) {
        console.error('❌ Failed to retrieve API logs:', {
            error: error.message,
            companyId,
        });
        return [];
    }
}
//# sourceMappingURL=apiLogService.js.map
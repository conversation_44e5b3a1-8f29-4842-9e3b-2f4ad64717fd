import { ProductionConfig } from '../types';
export declare function getXeroConfig(): {
    baseUrl: string;
    clientId: string;
    clientSecret: string;
    redirectUri: string;
};
export declare function getDatabaseConfig(): {
    databaseUrl: string;
};
export declare const PRODUCTION_CONFIG: ProductionConfig;
export declare function getStage(): string;
export declare function isProduction(): boolean;
export declare function getAwsRegion(): string;
//# sourceMappingURL=environment.d.ts.map
{"version": 3, "file": "extractTrialBalanceData.js", "sourceRoot": "", "sources": ["../../src/services/extractTrialBalanceData.ts"], "names": [], "mappings": ";;AAqBA,0DAmCC;AA0GD,gEAiBC;AA9JD,SAAgB,uBAAuB,CACnC,MAA8B,EAC9B,WAA4B,EAC5B,KAAsC;IAEtC,OAAO,CAAC,GAAG,CAAC,wCAAwC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;IAEjF,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,OAAO,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QACzD,OAAO,EAAE,CAAC;IACd,CAAC;IAED,MAAM,OAAO,GAAgC,EAAE,CAAC;IAGhD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAC5B,IAAI,GAAG,CAAC,OAAO,KAAK,SAAS,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YAExC,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;gBAC9B,MAAM,aAAa,GAAG,sBAAsB,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;gBAC3E,IAAI,aAAa,EAAE,CAAC;oBAChB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAChC,CAAC;YACL,CAAC;QACL,CAAC;aAAM,IAAI,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YAE/B,MAAM,aAAa,GAAG,sBAAsB,CAAC,GAAG,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;YACtE,IAAI,aAAa,EAAE,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO,CAAC,MAAM,8BAA8B,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;IACpG,OAAO,OAAO,CAAC;AACnB,CAAC;AAUD,SAAS,sBAAsB,CAC3B,GAAwB,EACxB,WAA4B,EAC5B,KAAsC;IAEtC,IAAI,GAAG,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC9D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,CAAC;QAED,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,WAAW,GAAG,WAAW,EAAE,KAAK,IAAI,IAAI,CAAC;QAE/C,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC;QAChB,CAAC;QAGD,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,WAAW,EAAE,UAAU,EAAE,CAAC;YAC1B,MAAM,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;YAC/E,SAAS,GAAG,WAAW,EAAE,KAAK,IAAI,IAAI,CAAC;QAC3C,CAAC;QAGD,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,CAAC,uCAAuC,WAAW,EAAE,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QAChB,CAAC;QAID,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAC9D,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QAO9D,MAAM,mBAAmB,GAAG,eAAe,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF,MAAM,oBAAoB,GAAG,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjF,MAAM,aAAa,GAA8B;YAC7C,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,WAAW,CAAC,IAAI,EAAE;YAC/B,MAAM,EAAE,eAAe;YACvB,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,mBAAmB,EAAE,mBAAmB;YACxC,oBAAoB,EAAE,oBAAoB;YAC1C,eAAe,EAAE,eAAe;SACnC,CAAC;QAEF,OAAO,aAAa,CAAC;IAEzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE;YACnD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;YAC/D,OAAO,EAAE,GAAG;SACf,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAQD,SAAS,WAAW,CAAC,YAAgC;IACjD,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAGD,IAAI,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;IAGxD,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3D,WAAW,GAAG,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;IACvC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;AACzC,CAAC;AAQD,SAAgB,0BAA0B,CAAC,MAAW;IAClD,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,OAAO,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QAC7D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC3E,OAAO,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QACtE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9B,OAAO,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC3D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC"}
/**
 * API Logging Service
 * 
 * Handles logging of API calls to the ApiLog table for monitoring and debugging
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
dotenv.config();

let prisma: PrismaClient | null = null;

/**
 * Get or create Prisma client instance
 */
function getPrismaClient(): PrismaClient {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'] as Array<'error' | 'warn'>,
            errorFormat: 'pretty' as const,
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new PrismaClient(config);
    }
    return prisma;
}

/**
 * API log entry data structure
 */
export interface ApiLogData {
    userId?: string;
    companyId: string;
    method?: string;
    apiUrl?: string;
    status?: string;
    integrationName?: string;
    duration?: string;
    apiName: string;
    apiRequest?: any;
    apiResponse?: any;
}

/**
 * Log an API call to the ApiLog table
 * 
 * @param logData - API log data
 */
export async function logApiCall(logData: ApiLogData): Promise<void> {
    try {
        console.log(`📝 Logging API call: ${logData.apiName} - Status: ${logData.status}`);

        await getPrismaClient().apiLog.create({
            data: {
                UserId: logData.userId || null,
                CompanyId: logData.companyId,
                Method: logData.method || null,
                ApiUrl: logData.apiUrl || null,
                Status: logData.status || null,
                IntegrationName: logData.integrationName || 'Xero',
                Duration: logData.duration || null,
                ApiName: logData.apiName,
                ApiRequest: logData.apiRequest || null,
                ApiResponse: logData.apiResponse || null,
                IsActive: true,
            },
        });

        console.log(`✅ API call logged successfully: ${logData.apiName}`);
    } catch (error: any) {
        console.error('❌ Failed to log API call:', {
            error: error.message,
            apiName: logData.apiName,
            companyId: logData.companyId,
        });
        // Don't throw error to avoid breaking the main flow
    }
}

/**
 * Log a successful API call
 * 
 * @param companyId - Company ID
 * @param method - HTTP method
 * @param apiUrl - API URL
 * @param duration - Request duration in milliseconds
 * @param apiName - API name/identifier
 * @param request - Request data
 * @param response - Response data
 * @param userId - Optional user ID
 */
export async function logSuccessfulApiCall(
    companyId: string,
    method: string,
    apiUrl: string,
    duration: number,
    apiName: string,
    request?: any,
    response?: any,
    userId?: string
): Promise<void> {
    await logApiCall({
        ...(userId && { userId }),
        companyId,
        method,
        apiUrl,
        status: 'SUCCESS',
        integrationName: 'Xero',
        duration: `${duration}ms`,
        apiName,
        apiRequest: request,
        apiResponse: response,
    });
}

/**
 * Log a failed API call
 * 
 * @param companyId - Company ID
 * @param method - HTTP method
 * @param apiUrl - API URL
 * @param duration - Request duration in milliseconds
 * @param apiName - API name/identifier
 * @param error - Error object
 * @param request - Request data
 * @param userId - Optional user ID
 */
export async function logFailedApiCall(
    companyId: string,
    method: string,
    apiUrl: string,
    duration: number,
    apiName: string,
    error: any,
    request?: any,
    userId?: string
): Promise<void> {
    await logApiCall({
        ...(userId && { userId }),
        companyId,
        method,
        apiUrl,
        status: 'ERROR',
        integrationName: 'Xero',
        duration: `${duration}ms`,
        apiName,
        apiRequest: request,
        apiResponse: {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
        },
    });
}

/**
 * Get recent API logs for a company
 * 
 * @param companyId - Company ID
 * @param limit - Number of logs to retrieve (default: 50)
 * @returns Promise<ApiLog[]> - Recent API logs
 */
export async function getRecentApiLogs(companyId: string, limit: number = 50) {
    try {
        const logs = await getPrismaClient().apiLog.findMany({
            where: {
                CompanyId: companyId,
                IsActive: true,
            },
            orderBy: {
                CreatedAt: 'desc',
            },
            take: limit,
        });

        return logs;
    } catch (error: any) {
        console.error('❌ Failed to retrieve API logs:', {
            error: error.message,
            companyId,
        });
        return [];
    }
}

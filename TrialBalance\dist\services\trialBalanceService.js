"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processTrialBalanceRequest = processTrialBalanceRequest;
const moment_1 = __importDefault(require("moment"));
const client_1 = require("@prisma/client");
const types_1 = require("../types");
const refreshTokenService_1 = require("./refreshTokenService");
const environment_1 = require("../config/environment");
const axiosInstance_1 = __importDefault(require("../utils/axiosInstance"));
const extractTrialBalanceData_1 = require("./extractTrialBalanceData");
const environment_2 = require("../config/environment");
const p_limit_1 = __importDefault(require("p-limit"));
const syncLogService_1 = require("./syncLogService");
const apiLogService_1 = require("./apiLogService");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
let prisma = null;
function getPrismaClient() {
    if (!prisma) {
        const config = {
            log: ['error', 'warn'],
            errorFormat: 'pretty',
        };
        if (process.env['IS_OFFLINE'] === 'true') {
            delete process.env['PRISMA_QUERY_ENGINE_LIBRARY'];
        }
        prisma = new client_1.PrismaClient(config);
    }
    return prisma;
}
async function processTrialBalanceRequest(requestData, _context) {
    const startTime = Date.now();
    console.log(`🚀 Starting Trial Balance sync for company: ${requestData.companyId}`);
    const syncLogData = {
        entity: 'TrialBalance',
        integration: 'Xero',
        companyId: requestData.companyId,
        requestPayload: requestData,
        maxRetries: 3,
    };
    const syncLogId = await (0, syncLogService_1.createSyncLog)(syncLogData);
    try {
        await (0, syncLogService_1.markSyncStarted)(syncLogId, 'Reports/TrialBalance', 'GET');
        const integration = await validateAndGetCompany(requestData.companyId);
        console.log(`✅ Company validation successful: ${integration.Id}`);
        await (0, refreshTokenService_1.refreshXeroToken)(integration.Id);
        console.log('✅ Access token validated/refreshed');
        const syncPeriod = await determineSyncPeriod(integration.Id, requestData);
        console.log(`📅 Sync period determined: ${syncPeriod.monthsToSync} months (${syncPeriod.isInitialSync ? 'Initial' : 'Regular'} sync)`);
        const monthsToProcess = generateMonthList(syncPeriod.startDate, syncPeriod.endDate);
        console.log(`📋 Generated ${monthsToProcess.length} months to process`);
        const concurrencyLimit = syncPeriod.isInitialSync ? 1 : environment_2.PRODUCTION_CONFIG.MAX_CONCURRENT_REQUESTS;
        const limit = (0, p_limit_1.default)(concurrencyLimit);
        console.log(`⚡ Processing with concurrency limit: ${concurrencyLimit}`);
        const processingPromises = monthsToProcess.map(month => limit(() => processTrialBalanceMonth(integration, requestData, month)));
        await Promise.all(processingPromises);
        const totalTime = Date.now() - startTime;
        console.log(`🎉 Trial Balance sync completed successfully in ${totalTime}ms for company: ${requestData.companyId}`);
        console.log(`📊 Processed ${monthsToProcess.length} months with ${concurrencyLimit} concurrent requests`);
        await (0, syncLogService_1.markSyncSuccess)(syncLogId, totalTime, `Successfully processed ${monthsToProcess.length} months of Trial Balance data`, { monthsProcessed: monthsToProcess.length, concurrencyLimit });
    }
    catch (error) {
        const totalTime = Date.now() - startTime;
        console.error(`❌ Trial Balance sync failed after ${totalTime}ms:`, {
            companyId: requestData.companyId,
            error: error.message,
            stack: error.stack,
        });
        await (0, syncLogService_1.markSyncFailed)(syncLogId, totalTime, error);
        throw error;
    }
    finally {
        await getPrismaClient().$disconnect();
    }
}
async function validateAndGetCompany(companyId) {
    console.log(`🔍 Validating company: ${companyId}`);
    if (!companyId || typeof companyId !== 'string') {
        throw new types_1.ValidationError('Invalid companyId provided');
    }
    const company = await getPrismaClient().company.findFirst({
        where: { Id: companyId },
        select: {
            Id: true,
            XeroAccessToken: true,
            XeroTenantId: true,
            XeroTokenExpiry: true,
            XeroRefreshToken: true,
            XeroRefreshTokenExpiry: true,
        },
    });
    if (!company) {
        throw new Error(`Company not found: ${companyId}`);
    }
    if (!company.XeroAccessToken || !company.XeroTenantId) {
        throw new Error(`Company ${companyId} is not connected to Xero. Please complete Xero integration first.`);
    }
    return company;
}
async function determineSyncPeriod(companyId, requestData) {
    console.log('📅 Determining sync period...');
    if (requestData.startDate && requestData.endDate) {
        const startDate = new Date(requestData.startDate);
        const endDate = new Date(requestData.endDate);
        const monthsDiff = (0, moment_1.default)(endDate).diff((0, moment_1.default)(startDate), 'months') + 1;
        return {
            startDate,
            endDate,
            monthsToSync: monthsDiff,
            isInitialSync: false,
        };
    }
    const existingDataCount = await getPrismaClient().trialBalance.count({
        where: { CompanyId: companyId },
    });
    const isInitialSync = existingDataCount === 0;
    const monthsToSync = isInitialSync ? 60 : 13;
    const endDate = new Date();
    const startDate = (0, moment_1.default)(endDate).subtract(monthsToSync - 1, 'months').startOf('month').toDate();
    console.log(`📊 Sync strategy: ${isInitialSync ? 'Initial' : 'Regular'} (${monthsToSync} months)`);
    return {
        startDate,
        endDate,
        monthsToSync,
        isInitialSync,
    };
}
function generateMonthList(startDate, endDate) {
    const months = [];
    const current = (0, moment_1.default)(startDate).startOf('month');
    const end = (0, moment_1.default)(endDate).startOf('month');
    while (current.isSameOrBefore(end)) {
        const monthStart = current.clone().startOf('month');
        const monthEnd = current.clone().endOf('month');
        months.push({
            year: current.year(),
            month: current.month() + 1,
            startDate: monthStart.format('YYYY-MM-DD'),
            endDate: monthEnd.format('YYYY-MM-DD'),
        });
        current.add(1, 'month');
    }
    return months;
}
async function processTrialBalanceMonth(integration, requestData, month) {
    const monthKey = `${month.year}-${month.month.toString().padStart(2, '0')}`;
    try {
        console.log(`📊 Processing Trial Balance for ${monthKey} (${month.endDate})`);
        if (!integration.XeroAccessToken || !integration.XeroTenantId) {
            throw new Error(`Missing Xero credentials for company: ${integration.Id}`);
        }
        const monthlyRequestData = {
            ...requestData,
            startDate: month.startDate,
            endDate: month.endDate,
        };
        console.log(`🔍 Fetching Trial Balance data for ${monthKey}`);
        const trialBalanceData = await getTrialBalance(integration.XeroAccessToken, integration.XeroTenantId, monthlyRequestData);
        await storeTrialBalanceData(integration.Id, month, trialBalanceData, monthKey);
        console.log(`✅ Successfully processed Trial Balance for ${monthKey}`);
    }
    catch (error) {
        console.error(`❌ Failed to process Trial Balance for ${monthKey}:`, {
            error: error.message,
            companyId: integration.Id,
            month: monthKey,
        });
        throw new Error(`Trial Balance processing failed for ${monthKey}: ${error.message}`);
    }
}
async function getTrialBalance(accessToken, tenantId, requestData) {
    const startTime = Date.now();
    try {
        const { baseUrl } = (0, environment_1.getXeroConfig)();
        const url = `${baseUrl}Reports/TrialBalance?date=${requestData.endDate}`;
        console.log(`🌐 API Request URL: ${url.replace(accessToken, '[REDACTED]')}`);
        const response = await axiosInstance_1.default.get(url, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Xero-tenant-id': tenantId,
                Accept: 'application/json',
                'User-Agent': 'TrialBalanceSync/1.0.0',
            },
            timeout: environment_2.PRODUCTION_CONFIG.API_TIMEOUT_MS,
        });
        const requestTime = Date.now() - startTime;
        console.log(`✅ Trial Balance API call completed in ${requestTime}ms`);
        await (0, apiLogService_1.logSuccessfulApiCall)(requestData.companyId, 'GET', url, requestTime, 'TrialBalance', { date: requestData.endDate, tenantId: tenantId.substring(0, 8) + '...' }, { recordCount: response.data?.Reports?.[0]?.Rows?.length || 0 });
        const reportData = response?.data;
        if (!reportData) {
            throw new Error('Empty response received from Xero API');
        }
        if (!reportData.Reports || !Array.isArray(reportData.Reports) || reportData.Reports.length === 0) {
            throw new Error('No reports found in Xero API response');
        }
        if (!reportData.Reports[0]) {
            throw new Error('Invalid Trial Balance report structure received from Xero');
        }
        if (!(0, extractTrialBalanceData_1.validateTrialBalanceReport)(reportData.Reports[0])) {
            throw new Error('Invalid Trial Balance report received from Xero');
        }
        const dateObj = new Date(requestData.endDate);
        const month = {
            year: dateObj.getFullYear(),
            month: dateObj.getMonth() + 1
        };
        console.log(`🔄 Processing Trial Balance data for ${month.year}-${month.month}`);
        const processedData = (0, extractTrialBalanceData_1.extractTrialBalanceData)(reportData.Reports[0], requestData, month);
        console.log(`✅ Processed ${processedData.length} Trial Balance rows`);
        return processedData;
    }
    catch (error) {
        const requestTime = Date.now() - startTime;
        console.error(`❌ Trial Balance API call failed after ${requestTime}ms:`, {
            error: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            date: requestData.endDate,
            tenantId: tenantId.substring(0, 8) + '...',
        });
        await (0, apiLogService_1.logFailedApiCall)(requestData.companyId, 'GET', `${(0, environment_1.getXeroConfig)().baseUrl}Reports/TrialBalance?date=${requestData.endDate}`, requestTime, 'TrialBalance', error, { date: requestData.endDate, tenantId: tenantId.substring(0, 8) + '...' });
        if (error.response?.status === 429) {
            const retryAfter = error.response.headers['retry-after'] || 60;
            throw new Error(`Xero API rate limit exceeded. Retry after ${retryAfter} seconds. Consider reducing concurrency.`);
        }
        if (error.response?.status === 401) {
            throw new Error('Xero API authentication failed. Token may be expired or invalid.');
        }
        if (error.response?.status === 403) {
            throw new Error('Xero API access forbidden. Check tenant permissions and subscription.');
        }
        if (error.response?.status === 404) {
            throw new Error('Trial Balance report not found. Check date parameters and Xero subscription.');
        }
        throw error;
    }
}
async function storeTrialBalanceData(companyId, month, trialBalanceData, monthKey) {
    console.log(`💾 Storing Trial Balance data for ${monthKey}...`);
    try {
        await getPrismaClient().$transaction(async (tx) => {
            if (trialBalanceData && trialBalanceData.length > 0) {
                console.log(`🗑️ Clearing existing Trial Balance data for ${monthKey}...`);
                await tx.trialBalance.deleteMany({
                    where: {
                        CompanyId: companyId,
                        Year: month.year,
                        Month: month.month,
                    },
                });
                console.log(`📝 Inserting ${trialBalanceData.length} Trial Balance rows for ${monthKey}...`);
                await tx.trialBalance.createMany({
                    data: trialBalanceData,
                    skipDuplicates: true,
                });
                console.log(`✅ Stored ${trialBalanceData.length} Trial Balance rows for ${monthKey}`);
            }
            else {
                console.log(`ℹ️ No Trial Balance data to store for ${monthKey}`);
            }
        });
        console.log(`💾 Database transaction completed successfully for ${monthKey}`);
    }
    catch (error) {
        console.error(`❌ Failed to store Trial Balance data for ${monthKey}:`, {
            error: error.message,
            companyId,
            dataCount: trialBalanceData?.length || 0,
        });
        throw new Error(`Database storage failed for ${monthKey}: ${error.message}`);
    }
}
//# sourceMappingURL=trialBalanceService.js.map